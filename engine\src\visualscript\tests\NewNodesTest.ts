/**
 * 新节点测试
 * 测试节点136-151的基本功能
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';
import { registerTimeNodes } from '../presets/TimeNodes';
import { registerAnimationNodes } from '../presets/AnimationNodes';
import { registerInputNodes } from '../presets/InputNodes';

/**
 * 测试新实现的节点
 */
export class NewNodesTest {
  private registry: NodeRegistry;

  constructor() {
    this.registry = new NodeRegistry();
    this.setupRegistry();
  }

  /**
   * 设置节点注册表
   */
  private setupRegistry(): void {
    // 注册新节点
    registerTimeNodes(this.registry);
    registerAnimationNodes(this.registry);
    registerInputNodes(this.registry);
  }

  /**
   * 测试时间节点
   */
  public testTimeNodes(): void {
    console.log('=== 测试时间节点 ===');

    // 测试获取当前时间节点
    const getCurrentTimeInfo = this.registry.getNodeTypeInfo('time/getCurrentTime');
    console.log('获取当前时间节点:', getCurrentTimeInfo?.label);

    // 测试获取帧时间节点
    const getDeltaTimeInfo = this.registry.getNodeTypeInfo('time/getDeltaTime');
    console.log('获取帧时间节点:', getDeltaTimeInfo?.label);

    // 测试延时执行节点
    const delayInfo = this.registry.getNodeTypeInfo('time/delay');
    console.log('延时执行节点:', delayInfo?.label);

    // 测试计时器节点
    const timerInfo = this.registry.getNodeTypeInfo('time/timer');
    console.log('计时器节点:', timerInfo?.label);

    // 测试秒表节点
    const stopwatchInfo = this.registry.getNodeTypeInfo('time/stopwatch');
    console.log('秒表节点:', stopwatchInfo?.label);

    // 测试格式化时间节点
    const formatTimeInfo = this.registry.getNodeTypeInfo('time/formatTime');
    console.log('格式化时间节点:', formatTimeInfo?.label);
  }

  /**
   * 测试动画节点
   */
  public testAnimationNodes(): void {
    console.log('=== 测试动画节点 ===');

    // 测试播放动画节点
    const playInfo = this.registry.getNodeTypeInfo('animation/play');
    console.log('播放动画节点:', playInfo?.label);

    // 测试停止动画节点
    const stopInfo = this.registry.getNodeTypeInfo('animation/stop');
    console.log('停止动画节点:', stopInfo?.label);

    // 测试暂停动画节点
    const pauseInfo = this.registry.getNodeTypeInfo('animation/pause');
    console.log('暂停动画节点:', pauseInfo?.label);

    // 测试恢复动画节点
    const resumeInfo = this.registry.getNodeTypeInfo('animation/resume');
    console.log('恢复动画节点:', resumeInfo?.label);

    // 测试设置动画速度节点
    const setSpeedInfo = this.registry.getNodeTypeInfo('animation/setSpeed');
    console.log('设置动画速度节点:', setSpeedInfo?.label);

    // 测试获取动画状态节点
    const getStateInfo = this.registry.getNodeTypeInfo('animation/getState');
    console.log('获取动画状态节点:', getStateInfo?.label);

    // 测试设置动画时间节点
    const setTimeInfo = this.registry.getNodeTypeInfo('animation/setTime');
    console.log('设置动画时间节点:', setTimeInfo?.label);

    // 测试动画混合节点
    const crossFadeInfo = this.registry.getNodeTypeInfo('animation/crossFade');
    console.log('动画混合节点:', crossFadeInfo?.label);
  }

  /**
   * 测试输入节点
   */
  public testInputNodes(): void {
    console.log('=== 测试输入节点 ===');

    // 测试按键按下节点
    const keyDownInfo = this.registry.getNodeTypeInfo('input/keyboard/isKeyDown');
    console.log('按键按下节点:', keyDownInfo?.label);

    // 测试按键释放节点
    const keyUpInfo = this.registry.getNodeTypeInfo('input/keyboard/isKeyUp');
    console.log('按键释放节点:', keyUpInfo?.label);
  }

  /**
   * 测试节点分类
   */
  public testNodeCategories(): void {
    console.log('=== 测试节点分类 ===');

    // 获取时间分类的节点
    const timeNodes = this.registry.getNodeTypesByCategory(NodeCategory.TIME);
    console.log('时间分类节点数量:', timeNodes.length);

    // 获取动画分类的节点
    const animationNodes = this.registry.getNodeTypesByCategory(NodeCategory.ANIMATION);
    console.log('动画分类节点数量:', animationNodes.length);

    // 获取输入分类的节点
    const inputNodes = this.registry.getNodeTypesByCategory(NodeCategory.INPUT);
    console.log('输入分类节点数量:', inputNodes.length);
  }

  /**
   * 运行所有测试
   */
  public runAllTests(): void {
    console.log('开始测试新实现的节点 (136-151)...');
    
    this.testTimeNodes();
    this.testAnimationNodes();
    this.testInputNodes();
    this.testNodeCategories();
    
    console.log('所有测试完成！');
  }
}

// 导出测试函数
export function testNewNodes(): void {
  const test = new NewNodesTest();
  test.runAllTests();
}
