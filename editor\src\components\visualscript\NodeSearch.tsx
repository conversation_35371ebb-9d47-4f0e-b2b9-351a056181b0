/**
 * 视觉脚本节点搜索组件
 * 提供节点搜索和过滤功能
 */
import React, { useState, useEffect, useRef } from 'react';
import { Input, List, Tag, Empty, Tabs, Button, Tooltip, Space, Typography} from 'antd';
import {
  SearchOutlined,
  StarOutlined,
  StarFilled,
  HistoryOutlined,
  TagOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// 移除引擎直接导入


const { Text } = Typography;

/**
 * 节点分类枚举
 */
enum NodeCategory {
  EVENTS = 'events',
  FLOW = 'flow',
  LOGIC = 'logic',
  MATH = 'math',
  STRING = 'string',
  DEBUG = 'debug',
  ENTITY = 'entity',
  TRANSFORM = 'transform',
  PHYSICS = 'physics',
  ANIMATION = 'animation',
  AUDIO = 'audio',
  INPUT = 'input',
  UI = 'ui',
  NETWORK = 'network',
  AI = 'ai',
  TIME = 'time',
  ARRAY = 'array',
  OBJECT = 'object',
  VARIABLE = 'variable',
  CONSTANT = 'constant',
  FUNCTION = 'function',
  CUSTOM = 'custom'
}

/**
 * 节点信息
 */
interface NodeInfo {
  /**
   * 节点类型
   */
  type: string;
  
  /**
   * 节点标签
   */
  label: string;
  
  /**
   * 节点描述
   */
  description: string;
  
  /**
   * 节点分类
   */
  category: NodeCategory;
  
  /**
   * 节点图标
   */
  icon: string;
  
  /**
   * 节点颜色
   */
  color: string;
  
  /**
   * 节点标签
   */
  tags: string[];
}

/**
 * 节点搜索属性
 */
interface NodeSearchProps {
  /**
   * 节点列表
   */
  nodes: NodeInfo[];
  
  /**
   * 收藏节点列表
   */
  favoriteNodes: string[];
  
  /**
   * 最近使用节点列表
   */
  recentNodes: string[];
  
  /**
   * 节点选择回调
   */
  onNodeSelect: (nodeType: string) => void;
  
  /**
   * 收藏节点回调
   */
  onToggleFavorite: (nodeType: string) => void;
}

/**
 * 节点搜索组件
 */
const NodeSearch: React.FC<NodeSearchProps> = ({
  nodes,
  favoriteNodes,
  recentNodes,
  onNodeSelect,
  onToggleFavorite
}) => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState<NodeCategory | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const searchInputRef = useRef<any>(null);

  // 定义主标签页配置
  const mainTabItems = [
    {
      key: 'all',
      label: t('节点搜索.全部'),
      children: filteredNodes.length > 0 ? (
        <List
          dataSource={filteredNodes}
          renderItem={renderNodeItem}
          style={{ maxHeight: 400, overflow: 'auto' }}
        />
      ) : (
        renderEmpty()
      )
    },
    {
      key: 'favorites',
      label: (
        <span>
          <StarFilled style={{ color: '#fadb14' }} /> {t('节点搜索.收藏')}
        </span>
      ),
      children: favoriteFilteredNodes.length > 0 ? (
        <List
          dataSource={favoriteFilteredNodes}
          renderItem={renderNodeItem}
          style={{ maxHeight: 400, overflow: 'auto' }}
        />
      ) : (
        renderEmpty()
      )
    },
    {
      key: 'recent',
      label: (
        <span>
          <HistoryOutlined /> {t('节点搜索.最近使用')}
        </span>
      ),
      children: recentFilteredNodes.length > 0 ? (
        <List
          dataSource={recentFilteredNodes}
          renderItem={renderNodeItem}
          style={{ maxHeight: 400, overflow: 'auto' }}
        />
      ) : (
        renderEmpty()
      )
    },
    {
      key: 'categories',
      label: (
        <span>
          <TagOutlined /> {t('节点搜索.分类')}
        </span>
      ),
      children: (
        <Tabs
          tabPosition="left"
          items={categories.map(category => ({
            key: category,
            label: t(`节点分类.${category}`),
            disabled: nodesByCategory[category].length === 0,
            children: nodesByCategory[category].length > 0 ? (
              <List
                dataSource={nodesByCategory[category]}
                renderItem={renderNodeItem}
                style={{ maxHeight: 400, overflow: 'auto' }}
              />
            ) : (
              renderEmpty()
            )
          }))}
        />
      )
    }
  ];
  
  // 所有标签
  const allTags = Array.from(new Set(nodes.flatMap(node => node.tags)));
  
  // 所有分类
  const categories = Object.values(NodeCategory);
  
  // 过滤节点
  const filteredNodes = nodes.filter(node => {
    // 搜索文本过滤
    const matchesSearch = searchText === '' || 
      node.label.toLowerCase().includes(searchText.toLowerCase()) ||
      node.description.toLowerCase().includes(searchText.toLowerCase()) ||
      node.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()));
    
    // 分类过滤
    const matchesCategory = selectedCategory === null || node.category === selectedCategory;
    
    // 标签过滤
    const matchesTags = selectedTags.length === 0 || 
      selectedTags.every(tag => node.tags.includes(tag));
    
    return matchesSearch && matchesCategory && matchesTags;
  });
  
  // 按标签过滤的节点
  const favoriteFilteredNodes = filteredNodes.filter(node => favoriteNodes.includes(node.type));
  
  // 按最近使用过滤的节点
  const recentFilteredNodes = filteredNodes.filter(node => recentNodes.includes(node.type));
  
  // 按分类分组的节点
  const nodesByCategory = categories.reduce((acc, category) => {
    acc[category] = filteredNodes.filter(node => node.category === category);
    return acc;
  }, {} as Record<NodeCategory, NodeInfo[]>);
  
  // 聚焦搜索框
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);
  
  /**
   * 渲染节点项
   */
  const renderNodeItem = (node: NodeInfo) => {
    const isFavorite = favoriteNodes.includes(node.type);
    
    return (
      <List.Item
        key={node.type}
        onClick={() => onNodeSelect(node.type)}
        actions={[
          <Tooltip title={isFavorite ? t('节点搜索.取消收藏') : t('节点搜索.收藏')}>
            <Button
              type="text"
              icon={isFavorite ? <StarFilled /> : <StarOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onToggleFavorite(node.type);
              }}
            />
          </Tooltip>
        ]}
      >
        <List.Item.Meta
          avatar={
            <div 
              style={{ 
                width: 24, 
                height: 24, 
                borderRadius: 4, 
                backgroundColor: node.color,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#fff'
              }}
            >
              {node.icon && <span className={`icon icon-${node.icon}`} />}
            </div>
          }
          title={node.label}
          description={
            <div>
              <Text type="secondary" ellipsis>{node.description}</Text>
              <div style={{ marginTop: 4 }}>
                {node.tags.map(tag => (
                  <Tag 
                    key={tag} 
                    color={selectedTags.includes(tag) ? 'blue' : undefined}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (selectedTags.includes(tag)) {
                        setSelectedTags(selectedTags.filter(t => t !== tag));
                      } else {
                        setSelectedTags([...selectedTags, tag]);
                      }
                    }}
                    style={{ cursor: 'pointer', marginBottom: 4 }}
                  >
                    {tag}
                  </Tag>
                ))}
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };
  
  /**
   * 渲染过滤器
   */
  const renderFilters = () => (
    <div style={{ marginBottom: 16 }}>
      <Space wrap>
        <Text strong>{t('节点搜索.分类')}:</Text>
        <Button
          type={selectedCategory === null ? 'primary' : 'default'}
          size="small"
          onClick={() => setSelectedCategory(null)}
        >
          {t('节点搜索.全部')}
        </Button>
        {categories.map(category => (
          <Button
            key={category}
            type={selectedCategory === category ? 'primary' : 'default'}
            size="small"
            onClick={() => setSelectedCategory(category)}
          >
            {t(`节点分类.${category}`)}
          </Button>
        ))}
      </Space>
      
      {selectedTags.length > 0 && (
        <div style={{ marginTop: 8 }}>
          <Space wrap>
            <Text strong>{t('节点搜索.已选标签')}:</Text>
            {selectedTags.map(tag => (
              <Tag 
                key={tag} 
                closable
                color="blue"
                onClose={() => setSelectedTags(selectedTags.filter(t => t !== tag))}
              >
                {tag}
              </Tag>
            ))}
            <Button
              type="text"
              size="small"
              icon={<CloseCircleOutlined />}
              onClick={() => setSelectedTags([])}
            >
              {t('节点搜索.清除标签')}
            </Button>
          </Space>
        </div>
      )}
    </div>
  );
  
  /**
   * 渲染标签云
   */
  const renderTagCloud = () => (
    <div style={{ marginBottom: 16 }}>
      <Text strong>{t('节点搜索.常用标签')}:</Text>
      <div style={{ marginTop: 8 }}>
        <Space wrap>
          {allTags.slice(0, 20).map(tag => (
            <Tag 
              key={tag} 
              color={selectedTags.includes(tag) ? 'blue' : undefined}
              onClick={() => {
                if (selectedTags.includes(tag)) {
                  setSelectedTags(selectedTags.filter(t => t !== tag));
                } else {
                  setSelectedTags([...selectedTags, tag]);
                }
              }}
              style={{ cursor: 'pointer' }}
            >
              {tag}
            </Tag>
          ))}
          {allTags.length > 20 && (
            <Tooltip title={t('节点搜索.更多标签')}>
              <Tag>...</Tag>
            </Tooltip>
          )}
        </Space>
      </div>
    </div>
  );
  
  /**
   * 渲染空状态
   */
  const renderEmpty = () => (
    <Empty
      image={Empty.PRESENTED_IMAGE_SIMPLE}
      description={t('节点搜索.无结果')}
    />
  );
  
  return (
    <div className="node-search">
      <div style={{ padding: '0 16px 16px' }}>
        <Input
          ref={searchInputRef}
          prefix={<SearchOutlined />}
          placeholder={t('节点搜索.搜索提示') || '搜索节点...'}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          allowClear
        />
      </div>
      
      {renderTagCloud()}
      {renderFilters()}
      
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={mainTabItems}
      />
    </div>
  );
};

export default NodeSearch;
