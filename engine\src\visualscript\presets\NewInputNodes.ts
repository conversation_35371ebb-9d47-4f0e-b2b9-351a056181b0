/**
 * 新增输入处理节点 (152-165)
 */

import { FunctionNode } from '../nodes/FunctionNode';
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 鼠标移动节点 (156)
 * 监听鼠标移动事件
 */
export class OnMouseMoveNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'onMove',
      type: SocketType.EVENT,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '移动时触发'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'deltaX',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'X偏移'
    });

    this.addOutput({
      name: 'deltaY',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'Y偏移'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      let lastX = 0;
      let lastY = 0;

      // 监听鼠标移动事件
      const handleMouseMove = (event: MouseEvent) => {
        const position = { x: event.clientX, y: event.clientY };
        const deltaX = event.clientX - lastX;
        const deltaY = event.clientY - lastY;

        this.setOutputValue('position', position);
        this.setOutputValue('deltaX', deltaX);
        this.setOutputValue('deltaY', deltaY);
        this.triggerEvent('onMove');

        lastX = event.clientX;
        lastY = event.clientY;
      };

      // 添加事件监听器
      document.addEventListener('mousemove', handleMouseMove);

      // 返回清理函数
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
      };
    } catch (error) {
      console.error('鼠标移动事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 鼠标滚轮节点 (157)
 * 监听鼠标滚轮事件
 */
export class OnMouseWheelNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'onWheel',
      type: SocketType.EVENT,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '滚轮时触发'
    });

    this.addOutput({
      name: 'deltaY',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'Y滚动'
    });

    this.addOutput({
      name: 'deltaX',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'X滚动'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      // 监听鼠标滚轮事件
      const handleWheel = (event: WheelEvent) => {
        const position = { x: event.clientX, y: event.clientY };

        this.setOutputValue('deltaY', event.deltaY);
        this.setOutputValue('deltaX', event.deltaX);
        this.setOutputValue('position', position);
        this.triggerEvent('onWheel');
      };

      // 添加事件监听器
      document.addEventListener('wheel', handleWheel);

      // 返回清理函数
      return () => {
        document.removeEventListener('wheel', handleWheel);
      };
    } catch (error) {
      console.error('鼠标滚轮事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 触摸点数量节点 (158)
 * 获取当前触摸点数量
 */
export class GetTouchCountNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'count',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      // 获取当前触摸点数量
      const touchCount = this.getCurrentTouchCount();

      this.setOutputValue('count', touchCount);
      return touchCount;
    } catch (error) {
      console.error('获取触摸点数量失败:', error);
      this.setOutputValue('count', 0);
      return 0;
    }
  }

  private getCurrentTouchCount(): number {
    // 这里应该连接到实际的触摸输入系统
    // 暂时返回0
    return 0;
  }
}

/**
 * 触摸位置节点 (159)
 * 获取指定触摸点位置
 */
export class GetTouchPositionNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加触摸索引输入
    this.addInput({
      name: 'touchIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '触摸索引',
      defaultValue: 0
    });

    // 添加输出插槽
    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'x',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'X坐标'
    });

    this.addOutput({
      name: 'y',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'Y坐标'
    });

    this.addOutput({
      name: 'isValid',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '有效'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const touchIndex = this.getInputValue('touchIndex') as number;

    try {
      // 获取指定触摸点位置
      const touchPosition = this.getTouchPosition(touchIndex);

      if (touchPosition) {
        this.setOutputValue('position', touchPosition);
        this.setOutputValue('x', touchPosition.x);
        this.setOutputValue('y', touchPosition.y);
        this.setOutputValue('isValid', true);
        return touchPosition;
      } else {
        const defaultPos = { x: 0, y: 0 };
        this.setOutputValue('position', defaultPos);
        this.setOutputValue('x', 0);
        this.setOutputValue('y', 0);
        this.setOutputValue('isValid', false);
        return defaultPos;
      }
    } catch (error) {
      console.error('获取触摸位置失败:', error);
      const defaultPos = { x: 0, y: 0 };
      this.setOutputValue('position', defaultPos);
      this.setOutputValue('x', 0);
      this.setOutputValue('y', 0);
      this.setOutputValue('isValid', false);
      return defaultPos;
    }
  }

  private getTouchPosition(index: number): { x: number, y: number } | null {
    // 这里应该连接到实际的触摸输入系统
    // 暂时返回null
    return null;
  }
}
